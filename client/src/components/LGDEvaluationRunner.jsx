import React, { useState } from 'react';

// Modal component for displaying full content
const ContentModal = ({ isOpen, onClose, title, content }) => {
  if (!isOpen) return null;

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10000
      }}
      onClick={onClose} // Close modal when clicking on the overlay
    >
      <div
        style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '20px',
          maxWidth: '80%',
          maxHeight: '80%',
          overflow: 'auto',
          position: 'relative'
        }}
        onClick={(e) => e.stopPropagation()} // Prevent clicks inside modal from closing it
      >
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '20px',
          borderBottom: '1px solid #eee',
          paddingBottom: '10px'
        }}>
          <h3 style={{ margin: 0 }}>{title}</h3>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              cursor: 'pointer',
              color: '#666'
            }}
          >
            ×
          </button>
        </div>
        <div style={{
          fontFamily: 'monospace',
          fontSize: '14px',
          lineHeight: '1.5',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word'
        }}>
          {content}
        </div>
      </div>
    </div>
  );
};

const LGDEvaluationRunner = ({ onRun }) => {
  const [transcript, setTranscript] = useState('');
  const [competencies, setCompetencies] = useState('');
  const [running, setRunning] = useState(false);
  const [lastResult, setLastResult] = useState(null);
  const [modalState, setModalState] = useState({
    isOpen: false,
    title: '',
    content: ''
  });

  const handleRun = async () => {
    if (!transcript.trim()) {
      alert('Please enter the LGD transcript');
      return;
    }
    if (!competencies.trim()) {
      alert('Please enter the competency guidelines');
      return;
    }

    try {
      setRunning(true);
      const result = await onRun({
        transcript: transcript.trim(),
        competencies: competencies.trim()
      });
      setLastResult(result);
    } catch (error) {
      alert('Failed to run LGD evaluation');
    } finally {
      setRunning(false);
    }
  };

  const openModal = (title, content) => {
    setModalState({
      isOpen: true,
      title,
      content
    });
  };

  const closeModal = () => {
    setModalState({
      isOpen: false,
      title: '',
      content: ''
    });
  };

  const loadSampleData = async () => {
    try {
      // Load transcript from data/lgd_transcript.txt (from backend server)
      const transcriptResponse = await fetch('http://localhost:3001/data/lgd_transcript.txt');
      if (!transcriptResponse.ok) {
        throw new Error(`Failed to fetch transcript: ${transcriptResponse.status}`);
      }
      const sampleTranscript = await transcriptResponse.text();

      // Load competencies from data/lgd_competency_guidelines.txt (from backend server)
      const competenciesResponse = await fetch('http://localhost:3001/data/lgd_competency_guidelines.txt');
      if (!competenciesResponse.ok) {
        throw new Error(`Failed to fetch competencies: ${competenciesResponse.status}`);
      }
      const sampleCompetencies = await competenciesResponse.text();

      setTranscript(sampleTranscript.trim());
      setCompetencies(sampleCompetencies.trim());
    } catch (error) {
      console.error('Error loading sample data:', error);
      alert('Failed to load sample data from files: ' + error.message);
    }
  };



  return (
    <div style={{
      border: '1px solid #ddd',
      borderRadius: '8px',
      padding: '20px',
      backgroundColor: '#f8f9fa'
    }}>
      <h3 style={{ marginBottom: '20px', color: '#495057' }}>
        🎯 LGD Analysis Evaluation Runner
      </h3>

      <div style={{ marginBottom: '20px' }}>
        <label style={{
          display: 'block',
          marginBottom: '8px',
          fontWeight: 'bold',
          color: '#495057'
        }}>
          LGD Transcript (JSON format):
        </label>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'flex-start' }}>
          <textarea
            value={transcript}
            onChange={(e) => setTranscript(e.target.value)}
            placeholder="Enter the LGD transcript in JSON format..."
            style={{
              flex: 1,
              minHeight: '120px',
              padding: '12px',
              border: '1px solid #ced4da',
              borderRadius: '4px',
              fontSize: '14px',
              fontFamily: 'monospace',
              resize: 'vertical'
            }}
          />
          {transcript && (
            <button
              onClick={() => openModal('LGD Transcript', transcript)}
              style={{
                padding: '8px 12px',
                border: '1px solid #007bff',
                borderRadius: '4px',
                backgroundColor: 'white',
                color: '#007bff',
                cursor: 'pointer',
                fontSize: '12px',
                whiteSpace: 'nowrap'
              }}
            >
              View Full
            </button>
          )}
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <label style={{
          display: 'block',
          marginBottom: '8px',
          fontWeight: 'bold',
          color: '#495057'
        }}>
          Competency Guidelines:
        </label>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'flex-start' }}>
          <textarea
            value={competencies}
            onChange={(e) => setCompetencies(e.target.value)}
            placeholder="Enter the competency assessment guidelines..."
            style={{
              flex: 1,
              minHeight: '120px',
              padding: '12px',
              border: '1px solid #ced4da',
              borderRadius: '4px',
              fontSize: '14px',
              resize: 'vertical'
            }}
          />
          {competencies && (
            <button
              onClick={() => openModal('Competency Guidelines', competencies)}
              style={{
                padding: '8px 12px',
                border: '1px solid #007bff',
                borderRadius: '4px',
                backgroundColor: 'white',
                color: '#007bff',
                cursor: 'pointer',
                fontSize: '12px',
                whiteSpace: 'nowrap'
              }}
            >
              View Full
            </button>
          )}
        </div>
      </div>

      <div style={{ display: 'flex', gap: '10px', marginBottom: '20px' }}>
        <button
          onClick={handleRun}
          disabled={running || !transcript.trim() || !competencies.trim()}
          style={{
            padding: '10px 20px',
            border: 'none',
            borderRadius: '4px',
            backgroundColor: running ? '#6c757d' : '#28a745',
            color: 'white',
            cursor: running ? 'not-allowed' : 'pointer',
            fontSize: '16px',
            fontWeight: 'bold'
          }}
        >
          {running ? 'Analyzing...' : 'Run LGD Analysis'}
        </button>

        <button
          onClick={loadSampleData}
          disabled={running}
          style={{
            padding: '10px 20px',
            border: '1px solid #007bff',
            borderRadius: '4px',
            backgroundColor: 'white',
            color: '#007bff',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          Load Sample Data
        </button>
      </div>

      {/* Processing notification */}
      {running && (
        <div style={{
          marginBottom: '20px',
          padding: '15px',
          backgroundColor: '#fff3cd',
          border: '1px solid #ffeaa7',
          borderRadius: '4px',
          color: '#856404'
        }}>
          <h4 style={{ margin: '0 0 10px 0', color: '#856404' }}>
            ⏳ Analysis in Progress
          </h4>
          <p style={{ margin: 0 }}>
            The LGD analysis is currently running. This process can take up to 10 minutes to complete.
            Please do not exit or refresh the page during this time.
          </p>
        </div>
      )}

      {lastResult && (
        <div style={{
          marginTop: '20px',
          padding: '15px',
          backgroundColor: '#d4edda',
          border: '1px solid #c3e6cb',
          borderRadius: '4px'
        }}>
          <h4 style={{ margin: '0 0 10px 0', color: '#155724' }}>
            ✅ Analysis Complete
          </h4>
          <p style={{ margin: 0, color: '#155724' }}>
            LGD analysis completed successfully. Check the results table below for detailed output.
          </p>
        </div>
      )}

      {/* Content Modal */}
      <ContentModal
        isOpen={modalState.isOpen}
        onClose={closeModal}
        title={modalState.title}
        content={modalState.content}
      />
    </div>
  );
};

export default LGDEvaluationRunner;
